class CreatePromoAllocation < ActiveRecord::Migration[8.0]
  def change
    create_table :promo_allocations do |t|
      # promo_id	promo_name	customer_id	customer	customer_group	customer_address	sales_area	region_sales	channel_report	avg_sales	avg_l3m	fy_2024	contribution	classification	budget	budget_owner	product_name	number_of_allocation	pic	gwp	gwp_allocation	gwp_description	shipping_receipt

      t.references :promo_lists, null: true, foreign_key: true
      t.string :promo_name
      t.string :customer_id
      t.string :customer
      t.string :customer_group
      t.string :customer_address
      t.string :sales_area
      t.string :region_sales
      t.string :channel_report
      t.float :avg_sales
      t.float :avg_l3m
      t.string :fy_2024
      t.float :contribution
      t.string :classification
      t.float :budget
      t.string :budget_owner
      t.string :product_name
      t.integer :number_of_allocation
      t.string :pic
      t.string :gwp
      t.integer :gwp_allocation
      t.string :gwp_description
      t.string :shipping_receipt

      t.timestamps
    end
  end
end
