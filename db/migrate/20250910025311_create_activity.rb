class CreateActivity < ActiveRecord::Migration[8.0]
  def change
    create_table :activities do |t|
      # promo_id	channel	brand	campaign_name	promo_name	start_date	end_date	objective	promo_description	detail_brief	allocation_link	posm	remarks
      t.references :promo_lists, null: true, foreign_key: true
      t.string :channel
      t.string :brand
      t.string :campaign_name
      t.string :promo_name
      t.date :start_date
      t.date :end_date
      t.string :objective
      t.text :promo_description
      t.string :detail_brief
      t.string :allocation_link
      t.string :posm
      t.string :remarks

      t.timestamps
    end
  end
end
