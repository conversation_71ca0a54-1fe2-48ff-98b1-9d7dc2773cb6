class CreateNpdAllocation < ActiveRecord::Migration[8.0]
  def change
    create_table :npd_allocations do |t|
      # npd_id	region	sales_area	dc_address	pic	pic_hp	prm	npd_allocation
      t.references :npd_revamps, null: true, foreign_key: true
      t.string :region
      t.string :sales_area
      t.string :dc_address
      t.string :pic
      t.string :pic_hp
      t.string :prm
      t.integer :npd_allocation

      t.timestamps
    end
  end
end
