class CreateUsers < ActiveRecord::Migration[8.0]
  def change
    create_table :users do |t|
      t.string :nik
      t.string :name
      t.references :company, null: true, foreign_key: true
      t.string :work_area
      t.string :position
      t.references :department, null: true, foreign_key: true
      t.references :division, null: true, foreign_key: true
      t.references :directorate, null: true, foreign_key: true
      t.references :education_level, null: true, foreign_key: true
      t.references :education_major, null: true, foreign_key: true

      t.timestamps
    end
    add_index :users, :nik
  end
end
