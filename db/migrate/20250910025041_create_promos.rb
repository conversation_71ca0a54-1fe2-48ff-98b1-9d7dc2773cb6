class CreatePromos < ActiveRecord::Migration[8.0]
  def change
    create_table :promos do |t|
      # promo_id	brand	channel	segment	sales_area	campaign_name	promo_name	promo_description	promo_type	target	ir	period	start_date	end_date	brief_link	budget	budget_owner	total_allocation	allocation_link	posm	design_link	rode	tfm/pmnj	remarks	tfm_code	tfm_link	pmnj_code	pmnj_link
      t.references :promo_list, null: true, foreign_key: true
      t.string :brand
      t.string :channel
      t.string :segment
      t.string :sales_area
      t.string :campaign_name
      t.string :promo_name
      t.text :promo_description
      t.string :promo_type
      t.string :target
      t.string :ir
      t.string :period
      t.date :start_date
      t.date :end_date
      t.string :brief_link
      t.float :budget
      t.string :budget_owner
      t.integer :total_allocation
      t.string :allocation_link
      t.string :posm
      t.string :design_link
      t.string :rode
      t.string :tfm_pmnj
      t.string :remarks
      t.string :tfm_code
      t.string :tfm_link
      t.string :pmnj_code
      t.string :pmnj_link

      t.timestamps
    end
  end
end
