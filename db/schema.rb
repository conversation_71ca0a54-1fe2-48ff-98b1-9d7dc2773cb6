# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_09_11_050351) do
  create_schema "onet"
  create_schema "shared_extensions"

  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"
  enable_extension "shared_extensions.hstore"
  enable_extension "shared_extensions.vector"

  create_table "activities", force: :cascade do |t|
    t.string "channel"
    t.string "brand"
    t.string "campaign_name"
    t.string "promo_name"
    t.date "start_date"
    t.date "end_date"
    t.string "objective"
    t.text "promo_description"
    t.string "detail_brief"
    t.string "allocation_link"
    t.string "posm"
    t.string "remarks"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "promo_id"
    t.index ["promo_id"], name: "index_activities_on_promo_id"
  end

  create_table "brand_segments", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_brand_segments_on_name"
  end

  create_table "brands", force: :cascade do |t|
    t.string "name"
    t.bigint "brand_segment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["brand_segment_id"], name: "index_brands_on_brand_segment_id"
    t.index ["name"], name: "index_brands_on_name"
  end

  create_table "channel_segments", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_channel_segments_on_name"
  end

  create_table "channels", force: :cascade do |t|
    t.string "name"
    t.bigint "channel_segment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["channel_segment_id"], name: "index_channels_on_channel_segment_id"
    t.index ["name"], name: "index_channels_on_name"
  end

  create_table "companies", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_companies_on_name"
  end

  create_table "customer_groups", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_customer_groups_on_name"
  end

  create_table "departments", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_departments_on_name"
  end

  create_table "directorates", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_directorates_on_name"
  end

  create_table "divisions", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_divisions_on_name"
  end

  create_table "education_levels", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_education_levels_on_name"
  end

  create_table "education_majors", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_education_majors_on_name"
  end

  create_table "general_sub_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_general_sub_categories_on_name"
  end

  create_table "group_regions", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_group_regions_on_name"
  end

  create_table "material_types", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_material_types_on_name"
  end

  create_table "npd_allocations", force: :cascade do |t|
    t.bigint "npd_revamps_id"
    t.string "region"
    t.string "sales_area"
    t.string "dc_address"
    t.string "pic"
    t.string "pic_hp"
    t.string "prm"
    t.integer "npd_allocation"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["npd_revamps_id"], name: "index_npd_allocations_on_npd_revamps_id"
  end

  create_table "npd_revamps", force: :cascade do |t|
    t.string "brand"
    t.string "product_name"
    t.string "listing_name"
    t.string "listing_platform"
    t.text "product_description"
    t.text "product_knowledge"
    t.float "landing_price"
    t.string "rtm"
    t.string "sku_details"
    t.string "item_code"
    t.string "product_barcode"
    t.string "product_odoo_code"
    t.float "price"
    t.string "visual_kv"
    t.date "start_date"
    t.date "end_date"
    t.integer "target_doors"
    t.string "customer"
    t.string "customer_group"
    t.string "customer_id"
    t.string "packshot"
    t.string "pdp_link"
    t.integer "koli"
    t.string "sp"
    t.string "pmnj"
    t.string "pmnj_link"
    t.string "posm"
    t.string "design_link"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "promo_allocations", force: :cascade do |t|
    t.string "promo_name"
    t.string "customer_id"
    t.string "customer"
    t.string "customer_group"
    t.string "customer_address"
    t.string "sales_area"
    t.string "region_sales"
    t.string "channel_report"
    t.float "avg_sales"
    t.float "avg_l3m"
    t.string "fy_2024"
    t.float "contribution"
    t.string "classification"
    t.float "budget"
    t.string "budget_owner"
    t.string "product_name"
    t.integer "number_of_allocation"
    t.string "pic"
    t.string "gwp"
    t.integer "gwp_allocation"
    t.string "gwp_description"
    t.string "shipping_receipt"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "promo_id"
    t.index ["promo_id"], name: "index_promo_allocations_on_promo_id"
  end

  create_table "promo_lists", primary_key: "promo_id", id: :string, force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["promo_id"], name: "index_promo_lists_on_promo_id", unique: true
  end

  create_table "promos", force: :cascade do |t|
    t.string "brand"
    t.string "channel"
    t.string "segment"
    t.string "sales_area"
    t.string "campaign_name"
    t.string "promo_name"
    t.text "promo_description"
    t.string "promo_type"
    t.string "target"
    t.string "ir"
    t.string "period"
    t.date "start_date"
    t.date "end_date"
    t.string "brief_link"
    t.float "budget"
    t.string "budget_owner"
    t.integer "total_allocation"
    t.string "allocation_link"
    t.string "posm"
    t.string "design_link"
    t.string "rode"
    t.string "tfm_pmnj"
    t.string "remarks"
    t.string "tfm_code"
    t.string "tfm_link"
    t.string "pmnj_code"
    t.string "pmnj_link"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "promo_id"
    t.index ["promo_id"], name: "index_promos_on_promo_id"
  end

  create_table "regions", force: :cascade do |t|
    t.string "name"
    t.bigint "group_region_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["group_region_id"], name: "index_regions_on_group_region_id"
    t.index ["name"], name: "index_regions_on_name"
  end

  create_table "sales_areas", force: :cascade do |t|
    t.string "name"
    t.bigint "region_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_sales_areas_on_name"
    t.index ["region_id"], name: "index_sales_areas_on_region_id"
  end

  create_table "skus", force: :cascade do |t|
    t.string "product_odoo_code"
    t.string "corporate_code"
    t.string "product"
    t.string "product_name"
    t.bigint "material_type_id"
    t.bigint "general_sub_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["general_sub_category_id"], name: "index_skus_on_general_sub_category_id"
    t.index ["material_type_id"], name: "index_skus_on_material_type_id"
    t.index ["product_odoo_code"], name: "index_skus_on_product_odoo_code"
  end

  create_table "stores", force: :cascade do |t|
    t.string "cust_id"
    t.string "name"
    t.bigint "customer_group_id"
    t.string "city"
    t.bigint "sales_area_id"
    t.bigint "channel_id"
    t.string "alt_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["channel_id"], name: "index_stores_on_channel_id"
    t.index ["cust_id"], name: "index_stores_on_cust_id"
    t.index ["customer_group_id"], name: "index_stores_on_customer_group_id"
    t.index ["sales_area_id"], name: "index_stores_on_sales_area_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "nik"
    t.string "name"
    t.bigint "company_id"
    t.string "work_area"
    t.string "position"
    t.bigint "department_id"
    t.bigint "division_id"
    t.bigint "directorate_id"
    t.bigint "education_level_id"
    t.bigint "education_major_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_users_on_company_id"
    t.index ["department_id"], name: "index_users_on_department_id"
    t.index ["directorate_id"], name: "index_users_on_directorate_id"
    t.index ["division_id"], name: "index_users_on_division_id"
    t.index ["education_level_id"], name: "index_users_on_education_level_id"
    t.index ["education_major_id"], name: "index_users_on_education_major_id"
    t.index ["nik"], name: "index_users_on_nik"
  end

  create_table "visibility_posms", force: :cascade do |t|
    t.string "promo_name"
    t.string "sales_area"
    t.string "customer"
    t.string "account"
    t.string "classification"
    t.string "customer_id"
    t.float "avg_sales"
    t.float "total_posm_budget"
    t.string "brand"
    t.string "channel"
    t.string "period"
    t.date "start_date"
    t.date "end_date"
    t.string "variant"
    t.string "objective"
    t.string "posm"
    t.float "budget_per_item"
    t.string "visual_kv"
    t.string "placement"
    t.string "posm_allocation"
    t.string "design_link"
    t.string "allocation_link"
    t.string "buffer"
    t.string "pmdb"
    t.string "tfm_code"
    t.string "series"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "promo_id"
    t.index ["promo_id"], name: "index_visibility_posms_on_promo_id"
  end

  add_foreign_key "brands", "brand_segments"
  add_foreign_key "channels", "channel_segments"
  add_foreign_key "npd_allocations", "npd_revamps", column: "npd_revamps_id"
  add_foreign_key "regions", "group_regions"
  add_foreign_key "sales_areas", "regions"
  add_foreign_key "skus", "general_sub_categories"
  add_foreign_key "skus", "material_types"
  add_foreign_key "stores", "channels"
  add_foreign_key "stores", "customer_groups"
  add_foreign_key "stores", "sales_areas"
  add_foreign_key "users", "companies"
  add_foreign_key "users", "departments"
  add_foreign_key "users", "directorates"
  add_foreign_key "users", "divisions"
  add_foreign_key "users", "education_levels"
  add_foreign_key "users", "education_majors"
end
