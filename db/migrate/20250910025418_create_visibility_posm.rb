class CreateVisibilityPosm < ActiveRecord::Migration[8.0]
  def change
    create_table :visibility_posms do |t|
      t.references :promo_lists, null: true, foreign_key: true

      # promo_id	promo_name	sales_area	customer	account	classification	customer_id	avg_sales	total_posm_budget	brand	channel	period	start_date	end_date	variant	objective	posm	budget_per_item	visual_kv	placement	posm_allocation	design_link	allocation_link	buffer	pmdb	tfm_code	series
      t.string :promo_name
      t.string :sales_area
      t.string :customer
      t.string :account
      t.string :classification
      t.string :customer_id
      t.float :avg_sales
      t.float :total_posm_budget
      t.string :brand
      t.string :channel
      t.string :period
      t.date :start_date
      t.date :end_date
      t.string :variant
      t.string :objective
      t.string :posm
      t.float :budget_per_item
      t.string :visual_kv
      t.string :placement
      t.string :posm_allocation
      t.string :design_link
      t.string :allocation_link
      t.string :buffer
      t.string :pmdb
      t.string :tfm_code
      t.string :series

      t.timestamps
    end
  end
end
