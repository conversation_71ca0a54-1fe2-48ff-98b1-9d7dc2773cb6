class CreateStores < ActiveRecord::Migration[8.0]
  def change
    create_table :stores do |t|
      t.string :cust_id
      t.string :name
      t.references :customer_group, null: true, foreign_key: true
      t.string :city
      t.references :sales_area, null: true, foreign_key: true
      t.references :channel, null: true, foreign_key: true
      t.string :alt_id

      t.timestamps
    end
    add_index :stores, :cust_id
  end
end
