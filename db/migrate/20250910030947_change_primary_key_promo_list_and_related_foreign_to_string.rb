class ChangePrimaryKeyPromoListAndRelatedForeignToString < ActiveRecord::Migration[8.0]
  def change
    remove_column :promos, :promo_list_id, :bigint
    remove_column :activities, :promo_lists_id, :bigint
    remove_column :promo_allocations, :promo_lists_id, :bigint
    remove_column :visibility_posms, :promo_lists_id, :bigint
    remove_column :promo_lists, :id, :bigint

    add_column :promo_lists, :promo_id, :string, primary_key: true
    change_column :promo_lists, :promo_id, :string, null: false
    add_index :promo_lists, :promo_id, unique: true

    add_column :promos, :promo_id, :string
    change_column :promos, :promo_id, :string, null: false
    add_index :promos, :promo_id, unique: true

    add_column :activities, :promo_id, :string
    change_column :activities, :promo_id, :string, null: false
    add_index :activities, :promo_id, unique: true

    add_column :promo_allocations, :promo_id, :string
    change_column :promo_allocations, :promo_id, :string, null: false
    add_index :promo_allocations, :promo_id, unique: true

    add_column :visibility_posms, :promo_id, :string
    change_column :visibility_posms, :promo_id, :string, null: false
    add_index :visibility_posms, :promo_id, unique: true
  end
end
