class CreateNpdRevamp < ActiveRecord::Migration[8.0]
  def change
    create_table :npd_revamps do |t|
      # npd_id	brand	product_name	listing_name	listing_platform	product_description	product_knowledge	landing_price	rtm	sku_details	item_code	product_barcode	product_odoo_code	price	visual_kv	start_date	end_date	target_doors	customer	customer_group	customer_id	packshot	pdp_link	koli	sp	pmnj	pmnj_link	posm	design_link

      t.string :brand
      t.string :product_name
      t.string :listing_name
      t.string :listing_platform
      t.text :product_description
      t.text :product_knowledge
      t.float :landing_price
      t.string :rtm
      t.string :sku_details
      t.string :item_code
      t.string :product_barcode
      t.string :product_odoo_code
      t.float :price
      t.string :visual_kv
      t.date :start_date
      t.date :end_date
      t.integer :target_doors
      t.string :customer
      t.string :customer_group
      t.string :customer_id
      t.string :packshot
      t.string :pdp_link
      t.integer :koli
      t.string :sp
      t.string :pmnj
      t.string :pmnj_link
      t.string :posm
      t.string :design_link

      t.timestamps
    end
  end
end
