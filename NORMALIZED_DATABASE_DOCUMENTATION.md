# Normalized Database Migration Documentation

## Overview

This document describes the normalized database schema created from the original Excel data structure. The database has been normalized to 3NF (Third Normal Form) to eliminate redundancy and improve data integrity.

## Original Data Structure

The original data was stored in an Excel file (`script/data.xlsx`) with the following sheets:

1. **users (cbd sheet)** - Employee information
2. **brands** - Brand and brand segment data  
3. **channels** - Channel and channel segment data
4. **sales_area** - Geographic hierarchy (group_region → region → area)
5. **stores** - Store/customer information
6. **sku** - Product/SKU information

## Normalized Schema (3NF)

### Lookup Tables

#### Brand Hierarchy
- **brand_segments** - Brand segment categories
- **brands** - Individual brands linked to segments

#### Channel Hierarchy  
- **channel_segments** - Channel segment categories
- **channels** - Individual channels linked to segments

#### Geographic Hierarchy
- **group_regions** - Top-level geographic groupings
- **regions** - Regional divisions within group regions
- **sales_areas** - Specific sales areas within regions

#### Store/Customer Related
- **customer_groups** - Customer group classifications
- **stores** - Individual stores/customers

#### Product Related
- **material_types** - Product material classifications
- **general_sub_categories** - Product sub-category classifications
- **skus** - Individual products/SKUs

#### User/Employee Related
- **companies** - Company information
- **departments** - Department classifications
- **divisions** - Division classifications  
- **directorates** - Directorate classifications
- **education_levels** - Education level classifications
- **education_majors** - Education major classifications
- **users** - Individual employees

## Database Migration Files

The following migration files were created:

```
db/migrate/20250911050206_create_brand_segments.rb
db/migrate/20250911050224_create_brands.rb
db/migrate/20250911050233_create_channel_segments.rb
db/migrate/20250911050239_create_channels.rb
db/migrate/20250911050245_create_group_regions.rb
db/migrate/20250911050250_create_regions.rb
db/migrate/20250911050256_create_sales_areas.rb
db/migrate/20250911050302_create_customer_groups.rb
db/migrate/20250911050308_create_stores.rb
db/migrate/20250911050314_create_material_types.rb
db/migrate/20250911050320_create_general_sub_categories.rb
db/migrate/20250911050326_create_skus.rb
db/migrate/20250911050332_create_companies.rb
db/migrate/20250911050334_create_departments.rb
db/migrate/20250911050335_create_divisions.rb
db/migrate/20250911050337_create_directorates.rb
db/migrate/20250911050343_create_education_levels.rb
db/migrate/20250911050345_create_education_majors.rb
db/migrate/20250911050351_create_users.rb
```

## Data Injection Script

**File:** `script/inject_normalized_data.rb`

This script reads the Excel file and populates the normalized database tables. It:

1. Clears existing data in dependency order
2. Processes each sheet and creates normalized records
3. Handles missing/null values appropriately
4. Creates lookup table entries as needed
5. Establishes proper foreign key relationships

## Model Associations

All Rails models have been created with proper associations:

### Example Associations:
- `BrandSegment` has_many `brands`
- `Brand` belongs_to `brand_segment`
- `Store` belongs_to `customer_group`, `sales_area`, `channel` (all optional)
- `User` belongs_to `company`, `department`, `division`, etc. (all optional)

## Data Summary

After successful migration and injection:

- **Brand Segments:** 4
- **Brands:** 14
- **Channel Segments:** 7
- **Channels:** 17
- **Group Regions:** 6
- **Regions:** 25
- **Sales Areas:** 41
- **Customer Groups:** 3,572
- **Stores:** 14,732
- **Material Types:** 1
- **General Sub Categories:** 4
- **SKUs:** 9,563
- **Companies:** 1
- **Departments:** 11
- **Divisions:** 8
- **Directorates:** 2
- **Education Levels:** 3
- **Education Majors:** 63
- **Users:** 103

## Usage Instructions

### Running the Migration
```bash
bin/rails db:migrate
```

### Injecting Data
```bash
ruby script/inject_normalized_data.rb
```

### Setting up Model Associations
```bash
ruby script/setup_model_associations.rb
```

## Benefits of Normalization

1. **Eliminated Redundancy:** No duplicate data across tables
2. **Improved Data Integrity:** Foreign key constraints ensure referential integrity
3. **Better Performance:** Smaller table sizes and proper indexing
4. **Easier Maintenance:** Changes to lookup values only need to be made in one place
5. **Scalability:** Structure supports future growth and additional relationships

## Testing

The normalized structure has been tested with sample queries demonstrating:
- Proper hierarchical relationships (brands, channels, geographic areas)
- Correct foreign key associations
- Data integrity across all tables
- Efficient querying with includes/joins

## Files Created

1. **Migration files:** 19 migration files for all tables
2. **Model files:** 19 Rails model files with associations
3. **Data injection script:** `script/inject_normalized_data.rb`
4. **Model setup script:** `script/setup_model_associations.rb`
5. **Documentation:** This file

The normalized database structure is now ready for use and provides a solid foundation for the application's data layer.
