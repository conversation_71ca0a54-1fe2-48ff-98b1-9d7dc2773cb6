class CreateSkus < ActiveRecord::Migration[8.0]
  def change
    create_table :skus do |t|
      t.string :product_odoo_code
      t.string :corporate_code
      t.string :product
      t.string :product_name
      t.references :material_type, null: true, foreign_key: true
      t.references :general_sub_category, null: true, foreign_key: true

      t.timestamps
    end
    add_index :skus, :product_odoo_code
  end
end
